import { prisma } from './prisma';
import { miningUnitDb, transactionDb, systemLogDb } from './database';

// Update user active status based on mining units
export async function updateUserActiveStatus(userId: string) {
  try {
    // Check if user has any active mining units
    const activeMiningUnits = await prisma.miningUnit.count({
      where: {
        userId,
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
    });

    // User is active if they have at least 1 active mining unit
    const isActive = activeMiningUnits > 0;

    // Update user status
    await prisma.user.update({
      where: { id: userId },
      data: { isActive },
    });

    return isActive;
  } catch (error) {
    console.error('Error updating user active status:', error);
    throw error;
  }
}

// Update all users' active status based on their mining units
export async function updateAllUsersActiveStatus() {
  try {
    console.log('Starting bulk user active status update...');

    // Get all users
    const users = await prisma.user.findMany({
      select: { id: true },
    });

    const results = [];

    for (const user of users) {
      try {
        const isActive = await updateUserActiveStatus(user.id);
        results.push({ userId: user.id, isActive });
      } catch (error) {
        console.error(`Error updating status for user ${user.id}:`, error);
      }
    }

    // Log the bulk update
    await systemLogDb.create({
      action: 'BULK_USER_STATUS_UPDATE',
      details: {
        usersProcessed: results.length,
        activeUsers: results.filter(r => r.isActive).length,
        inactiveUsers: results.filter(r => !r.isActive).length,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Updated status for ${results.length} users`);
    return results;
  } catch (error) {
    console.error('Error in bulk user status update:', error);
    throw error;
  }
}

// Calculate daily ROI for all active mining units
export async function calculateDailyROI() {
  try {
    console.log('Starting daily ROI calculation...');

    // Get all active mining units
    const activeMiningUnits = await prisma.miningUnit.findMany({
      where: {
        status: 'ACTIVE',
        expiryDate: {
          gt: new Date(),
        },
      },
      include: {
        user: true,
      },
    });

    console.log(`Found ${activeMiningUnits.length} active mining units`);

    const results = [];

    for (const unit of activeMiningUnits) {
      try {
        // Calculate daily earnings
        const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;

        // Check if unit should expire (5x investment earned)
        const totalEarningsAfter = unit.totalEarned + dailyEarnings;
        const maxEarnings = unit.investmentAmount * 5;

        let finalEarnings = dailyEarnings;
        let shouldExpire = false;

        if (totalEarningsAfter >= maxEarnings) {
          // Cap earnings at 5x investment
          finalEarnings = maxEarnings - unit.totalEarned;
          shouldExpire = true;
        }

        if (finalEarnings > 0) {
          // Update mining unit total earned
          await miningUnitDb.updateTotalEarned(unit.id, finalEarnings);

          // Create pending earnings transaction
          await transactionDb.create({
            userId: unit.userId,
            type: 'MINING_EARNINGS',
            amount: finalEarnings,
            description: `Daily mining earnings - ${unit.thsAmount} TH/s`,
            status: 'PENDING',
          });

          results.push({
            unitId: unit.id,
            userId: unit.userId,
            earnings: finalEarnings,
            expired: shouldExpire,
          });
        }

        // Expire unit if necessary
        if (shouldExpire) {
          await miningUnitDb.expireUnit(unit.id);

          // Update user active status after unit expiration
          await updateUserActiveStatus(unit.userId);

          await systemLogDb.create({
            action: 'MINING_UNIT_EXPIRED',
            userId: unit.userId,
            details: {
              miningUnitId: unit.id,
              reason: '5x_investment_reached',
              totalEarned: totalEarningsAfter,
              investmentAmount: unit.investmentAmount,
            },
          });
        }

      } catch (unitError) {
        console.error(`Error processing unit ${unit.id}:`, unitError);
      }
    }

    // Log the daily ROI calculation
    await systemLogDb.create({
      action: 'DAILY_ROI_CALCULATED',
      details: {
        unitsProcessed: activeMiningUnits.length,
        totalEarnings: results.reduce((sum, r) => sum + r.earnings, 0),
        expiredUnits: results.filter(r => r.expired).length,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Daily ROI calculation completed. Processed ${results.length} units.`);
    return results;

  } catch (error) {
    console.error('Daily ROI calculation error:', error);
    throw error;
  }
}

// Process weekly earnings distribution (Saturday 15:00 UTC)
export async function processWeeklyEarnings() {
  try {
    console.log('Starting weekly earnings distribution...');

    // Get all pending mining earnings
    const pendingEarnings = await prisma.transaction.findMany({
      where: {
        type: 'MINING_EARNINGS',
        status: 'PENDING',
      },
      include: {
        user: true,
      },
    });

    console.log(`Found ${pendingEarnings.length} pending earnings transactions`);

    const userEarnings = new Map<string, number>();

    // Group earnings by user
    for (const transaction of pendingEarnings) {
      const currentTotal = userEarnings.get(transaction.userId) || 0;
      userEarnings.set(transaction.userId, currentTotal + transaction.amount);
    }

    const results = [];

    // Process each user's earnings
    for (const [userId, totalEarnings] of userEarnings) {
      try {
        // Mark all pending transactions as completed
        await prisma.transaction.updateMany({
          where: {
            userId,
            type: 'MINING_EARNINGS',
            status: 'PENDING',
          },
          data: {
            status: 'COMPLETED',
          },
        });

        results.push({
          userId,
          totalEarnings,
        });

      } catch (userError) {
        console.error(`Error processing earnings for user ${userId}:`, userError);
      }
    }

    // Log the weekly distribution
    await systemLogDb.create({
      action: 'WEEKLY_EARNINGS_DISTRIBUTED',
      details: {
        usersProcessed: results.length,
        totalDistributed: results.reduce((sum, r) => sum + r.totalEarnings, 0),
        transactionsProcessed: pendingEarnings.length,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);
    return results;

  } catch (error) {
    console.error('Weekly earnings distribution error:', error);
    throw error;
  }
}

// Check and expire mining units that have reached 12 months
export async function expireOldMiningUnits() {
  try {
    console.log('Checking for expired mining units...');

    const expiredUnits = await prisma.miningUnit.findMany({
      where: {
        status: 'ACTIVE',
        expiryDate: {
          lte: new Date(),
        },
      },
    });

    console.log(`Found ${expiredUnits.length} units to expire`);

    for (const unit of expiredUnits) {
      await miningUnitDb.expireUnit(unit.id);

      // Update user active status after unit expiration
      await updateUserActiveStatus(unit.userId);

      await systemLogDb.create({
        action: 'MINING_UNIT_EXPIRED',
        userId: unit.userId,
        details: {
          miningUnitId: unit.id,
          reason: '12_months_reached',
          totalEarned: unit.totalEarned,
          investmentAmount: unit.investmentAmount,
        },
      });
    }

    return expiredUnits.length;

  } catch (error) {
    console.error('Mining unit expiry check error:', error);
    throw error;
  }
}

// Get mining statistics
export async function getMiningStats() {
  try {
    const stats = await prisma.$transaction([
      // Total TH/s sold
      prisma.miningUnit.aggregate({
        _sum: {
          thsAmount: true,
        },
      }),
      
      // Active TH/s
      prisma.miningUnit.aggregate({
        where: {
          status: 'ACTIVE',
        },
        _sum: {
          thsAmount: true,
        },
      }),
      
      // Total investment
      prisma.miningUnit.aggregate({
        _sum: {
          investmentAmount: true,
        },
      }),
      
      // Total earnings distributed
      prisma.transaction.aggregate({
        where: {
          type: 'MINING_EARNINGS',
          status: 'COMPLETED',
        },
        _sum: {
          amount: true,
        },
      }),
      
      // Active mining units count
      prisma.miningUnit.count({
        where: {
          status: 'ACTIVE',
        },
      }),
      
      // Total mining units count
      prisma.miningUnit.count(),
    ]);

    return {
      totalTHSSold: stats[0]._sum.thsAmount || 0,
      activeTHS: stats[1]._sum.thsAmount || 0,
      totalInvestment: stats[2]._sum.investmentAmount || 0,
      totalEarningsDistributed: stats[3]._sum.amount || 0,
      activeMiningUnits: stats[4],
      totalMiningUnits: stats[5],
    };

  } catch (error) {
    console.error('Mining stats error:', error);
    throw error;
  }
}

// Calculate user's estimated earnings
export async function calculateEstimatedEarnings(userId: string) {
  try {
    const activeMiningUnits = await miningUnitDb.findActiveByUserId(userId);
    
    if (activeMiningUnits.length === 0) {
      return {
        next7Days: 0,
        next30Days: 0,
        next365Days: 0,
      };
    }

    let totalDaily = 0;
    
    for (const unit of activeMiningUnits) {
      const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;
      const maxEarnings = unit.investmentAmount * 5;
      const remainingEarnings = maxEarnings - unit.totalEarned;
      
      // Use the lower of daily earnings or remaining earnings
      totalDaily += Math.min(dailyEarnings, remainingEarnings);
    }

    return {
      next7Days: totalDaily * 7,
      next30Days: totalDaily * 30,
      next365Days: totalDaily * 365,
    };

  } catch (error) {
    console.error('Estimated earnings calculation error:', error);
    throw error;
  }
}
