module.exports = {

"[project]/.next-internal/server/app/api/referrals/tree/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "adminSettingsDb": (()=>adminSettingsDb),
    "binaryPointsDb": (()=>binaryPointsDb),
    "depositTransactionDb": (()=>depositTransactionDb),
    "miningUnitDb": (()=>miningUnitDb),
    "referralDb": (()=>referralDb),
    "supportTicketDb": (()=>supportTicketDb),
    "systemLogDb": (()=>systemLogDb),
    "ticketResponseDb": (()=>ticketResponseDb),
    "transactionDb": (()=>transactionDb),
    "userDb": (()=>userDb),
    "walletBalanceDb": (()=>walletBalanceDb),
    "withdrawalDb": (()=>withdrawalDb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
const userDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.create({
            data: {
                email: data.email,
                firstName: data.firstName,
                lastName: data.lastName,
                password: data.password,
                referralId: data.referralId || undefined
            }
        });
    },
    async findByEmail (email) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                email
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findById (id) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id
            },
            include: {
                miningUnits: true,
                transactions: true,
                binaryPoints: true
            }
        });
    },
    async findByReferralId (referralId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                referralId
            }
        });
    },
    async update (id, data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id
            },
            data
        });
    },
    async updateKYCStatus (userId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: userId
            },
            data: {
                kycStatus: status
            }
        });
    }
};
const miningUnitDb = {
    async create (data) {
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 12 months from now
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.create({
            data: {
                userId: data.userId,
                thsAmount: data.thsAmount,
                investmentAmount: data.investmentAmount,
                dailyROI: data.dailyROI,
                expiryDate
            }
        });
    },
    async findActiveByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.findMany({
            where: {
                userId,
                status: 'ACTIVE',
                expiryDate: {
                    gt: new Date()
                }
            }
        });
    },
    async updateTotalEarned (unitId, amount) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                totalEarned: {
                    increment: amount
                }
            }
        });
    },
    async expireUnit (unitId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.update({
            where: {
                id: unitId
            },
            data: {
                status: 'EXPIRED'
            }
        });
    }
};
const transactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.create({
            data: {
                userId: data.userId,
                type: data.type,
                amount: data.amount,
                description: data.description,
                status: data.status || 'PENDING'
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.types && filters.types.length > 0) {
            where.type = {
                in: filters.types
            };
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        const include = filters?.includeUser ? {
            user: {
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true
                }
            }
        } : undefined;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.findMany({
            where,
            include,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset
        });
    },
    async updateStatus (transactionId, status) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].transaction.update({
            where: {
                id: transactionId
            },
            data: {
                status
            }
        });
    }
};
const referralDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.create({
            data: {
                referrerId: data.referrerId,
                referredId: data.referredId,
                placementSide: data.placementSide
            }
        });
    },
    async findByReferrerId (referrerId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
            where: {
                referrerId
            },
            include: {
                referred: {
                    select: {
                        id: true,
                        email: true,
                        createdAt: true
                    }
                }
            }
        });
    }
};
const binaryPointsDb = {
    async upsert (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.upsert({
            where: {
                userId: data.userId
            },
            update: {
                leftPoints: data.leftPoints !== undefined ? {
                    increment: data.leftPoints
                } : undefined,
                rightPoints: data.rightPoints !== undefined ? {
                    increment: data.rightPoints
                } : undefined
            },
            create: {
                userId: data.userId,
                leftPoints: data.leftPoints || 0,
                rightPoints: data.rightPoints || 0
            }
        });
    },
    async findByUserId (userId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.findUnique({
            where: {
                userId
            }
        });
    },
    async resetPoints (userId, leftPoints, rightPoints) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.update({
            where: {
                userId
            },
            data: {
                leftPoints,
                rightPoints,
                flushDate: new Date()
            }
        });
    }
};
const withdrawalDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.create({
            data: {
                userId: data.userId,
                amount: data.amount,
                usdtAddress: data.usdtAddress
            }
        });
    },
    async findPending () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.findMany({
            where: {
                status: 'PENDING'
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        kycStatus: true
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    },
    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].withdrawalRequest.update({
            where: {
                id: requestId
            },
            data: {
                status,
                processedBy,
                txid,
                rejectionReason,
                processedAt: new Date()
            }
        });
    }
};
const adminSettingsDb = {
    async get (key) {
        const setting = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findUnique({
            where: {
                key
            }
        });
        return setting?.value;
    },
    async set (key, value, updatedBy) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.upsert({
            where: {
                key
            },
            update: {
                value
            },
            create: {
                key,
                value
            }
        });
    },
    async getAll () {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].adminSettings.findMany();
    }
};
const systemLogDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].systemLog.create({
            data: {
                action: data.action,
                userId: data.userId,
                adminId: data.adminId,
                details: data.details ? JSON.stringify(data.details) : null,
                ipAddress: data.ipAddress,
                userAgent: data.userAgent
            }
        });
    }
};
const walletBalanceDb = {
    async getOrCreate (userId) {
        let walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.findUnique({
            where: {
                userId
            }
        });
        if (!walletBalance) {
            walletBalance = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.create({
                data: {
                    userId,
                    availableBalance: 0,
                    pendingBalance: 0,
                    totalDeposits: 0,
                    totalWithdrawals: 0,
                    totalEarnings: 0
                }
            });
        }
        return walletBalance;
    },
    async updateBalance (userId, updates) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                ...updates,
                lastUpdated: new Date()
            }
        });
    },
    async addDeposit (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalDeposits: wallet.totalDeposits + amount,
                lastUpdated: new Date()
            }
        });
    },
    async addEarnings (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance + amount,
                totalEarnings: wallet.totalEarnings + amount,
                lastUpdated: new Date()
            }
        });
    },
    async deductWithdrawal (userId, amount) {
        const wallet = await this.getOrCreate(userId);
        if (wallet.availableBalance < amount) {
            throw new Error('Insufficient balance');
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].walletBalance.update({
            where: {
                userId
            },
            data: {
                availableBalance: wallet.availableBalance - amount,
                totalWithdrawals: wallet.totalWithdrawals + amount,
                lastUpdated: new Date()
            }
        });
    },
    async findByUserId (userId) {
        return await this.getOrCreate(userId);
    }
};
const depositTransactionDb = {
    async create (data) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.create({
            data: {
                userId: data.userId,
                transactionId: data.transactionId,
                amount: data.amount,
                usdtAmount: data.usdtAmount,
                tronAddress: data.tronAddress,
                senderAddress: data.senderAddress,
                blockNumber: data.blockNumber,
                blockTimestamp: data.blockTimestamp,
                confirmations: data.confirmations || 0,
                status: 'PENDING'
            }
        });
    },
    async findByTransactionId (transactionId) {
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findUnique({
            where: {
                transactionId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findByUserId (userId, filters) {
        const where = {
            userId
        };
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 50,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async findAll (filters) {
        const where = {};
        if (filters?.status) {
            where.status = filters.status;
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            take: filters?.limit || 100,
            skip: filters?.offset,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    async updateStatus (transactionId, status, updates) {
        const updateData = {
            status
        };
        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;
        if (updates?.processedAt) updateData.processedAt = updates.processedAt;
        if (updates?.failureReason) updateData.failureReason = updates.failureReason;
        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.update({
            where: {
                transactionId
            },
            data: updateData
        });
    },
    async markAsCompleted (transactionId) {
        return await this.updateStatus(transactionId, 'COMPLETED', {
            processedAt: new Date()
        });
    },
    async markAsFailed (transactionId, reason) {
        return await this.updateStatus(transactionId, 'FAILED', {
            failureReason: reason,
            processedAt: new Date()
        });
    },
    async getPendingDeposits () {
        return await this.findAll({
            status: 'PENDING'
        });
    },
    async getDepositStats () {
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.aggregate({
            _count: {
                id: true
            },
            _sum: {
                usdtAmount: true
            },
            where: {
                status: 'COMPLETED'
            }
        });
        const pendingCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].depositTransaction.count({
            where: {
                status: 'PENDING'
            }
        });
        return {
            totalDeposits: stats._count.id || 0,
            totalAmount: stats._sum.usdtAmount || 0,
            pendingDeposits: pendingCount
        };
    }
};
const supportTicketDb = {
    create: async (data)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.create({
            data,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    },
    findByUserId: async (userId)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findMany({
            where: {
                userId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    },
    findById: async (id)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findUnique({
            where: {
                id
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    },
    findAll: async ()=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    },
    updateStatus: async (id, status)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].supportTicket.update({
            where: {
                id
            },
            data: {
                status,
                updatedAt: new Date()
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                },
                responses: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'asc'
                    }
                }
            }
        });
    }
};
const ticketResponseDb = {
    create: async (data)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ticketResponse.create({
            data,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            }
        });
    },
    findByTicketId: async (ticketId)=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ticketResponse.findMany({
            where: {
                ticketId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    }
};
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authenticateRequest": (()=>authenticateRequest),
    "createSession": (()=>createSession),
    "generateReferralId": (()=>generateReferralId),
    "generateToken": (()=>generateToken),
    "hashPassword": (()=>hashPassword),
    "isAdmin": (()=>isAdmin),
    "loginUser": (()=>loginUser),
    "registerUser": (()=>registerUser),
    "validateEmail": (()=>validateEmail),
    "validatePassword": (()=>validatePassword),
    "validateSession": (()=>validateSession),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const hashPassword = async (password)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, 12);
};
const verifyPassword = async (password, hashedPassword)=>{
    return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
};
const generateToken = (payload)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN
    });
};
const verifyToken = (token)=>{
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        return null;
    }
};
const generateReferralId = ()=>{
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = 'HC'; // HashCoreX prefix
    for(let i = 0; i < 8; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};
const authenticateRequest = async (request)=>{
    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;
    if (!token) {
        return {
            authenticated: false,
            user: null
        };
    }
    const decoded = verifyToken(token);
    if (!decoded) {
        return {
            authenticated: false,
            user: null
        };
    }
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(decoded.email);
    if (!user || !user.isActive) {
        return {
            authenticated: false,
            user: null
        };
    }
    return {
        authenticated: true,
        user
    };
};
const registerUser = async (data)=>{
    // Check if user already exists
    const existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (existingUser) {
        throw new Error('User already exists with this email');
    }
    // Validate referral code if provided
    let referrerId;
    if (data.referralCode) {
        const referrer = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(data.referralCode);
        if (!referrer) {
            throw new Error('Invalid referral code');
        }
        referrerId = referrer.id;
    }
    // Hash password
    const passwordHash = await hashPassword(data.password);
    // Generate unique referral ID
    let referralId;
    let isUnique = false;
    do {
        referralId = generateReferralId();
        const existing = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByReferralId(referralId);
        isUnique = !existing;
    }while (!isUnique)
    // Create user in PostgreSQL
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].create({
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        password: passwordHash,
        referralId
    });
    // Create referral relationship if referrer exists
    if (referrerId) {
        const { placeUserInBinaryTree, placeUserInSpecificSide } = await __turbopack_context__.r("[project]/src/lib/referral.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        if (data.placementSide) {
            // Place user in specific side if requested
            await placeUserInSpecificSide(referrerId, user.id, data.placementSide.toUpperCase());
        } else {
            // Place user in weaker leg automatically
            await placeUserInBinaryTree(referrerId, user.id);
        }
    }
    return {
        id: user.id,
        email: user.email,
        referralId: user.referralId,
        kycStatus: user.kycStatus
    };
};
const loginUser = async (data)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findByEmail(data.email);
    if (!user) {
        throw new Error('Invalid email or password');
    }
    if (!user.isActive) {
        throw new Error('Account is deactivated');
    }
    const isValidPassword = await verifyPassword(data.password, user.password);
    if (!isValidPassword) {
        throw new Error('Invalid email or password');
    }
    const token = generateToken({
        userId: user.id,
        email: user.email
    });
    return {
        token,
        user: {
            id: user.id,
            email: user.email,
            referralId: user.referralId,
            kycStatus: user.kycStatus
        }
    };
};
const validatePassword = (password)=>{
    const errors = [];
    if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        errors.push('Password must contain at least one special character');
    }
    return {
        valid: errors.length === 0,
        errors
    };
};
const validateEmail = (email)=>{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
const createSession = (userId, email)=>{
    return generateToken({
        userId,
        email
    });
};
const validateSession = (token)=>{
    return verifyToken(token);
};
const isAdmin = async (userId)=>{
    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["userDb"].findById(userId);
    return user?.role === 'ADMIN';
};
}}),
"[project]/src/lib/referral.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addBinaryPoints": (()=>addBinaryPoints),
    "bulkUpdateTreeCounts": (()=>bulkUpdateTreeCounts),
    "getBinaryTreeStructure": (()=>getBinaryTreeStructure),
    "getCachedDownlineCounts": (()=>getCachedDownlineCounts),
    "getDetailedTeamStats": (()=>getDetailedTeamStats),
    "getDirectReferralCount": (()=>getDirectReferralCount),
    "getSponsorInfo": (()=>getSponsorInfo),
    "getTotalTeamCount": (()=>getTotalTeamCount),
    "getTreeHealthStats": (()=>getTreeHealthStats),
    "getUsersByGeneration": (()=>getUsersByGeneration),
    "loadNodeChildren": (()=>loadNodeChildren),
    "placeUserInBinaryTree": (()=>placeUserInBinaryTree),
    "placeUserInSpecificSide": (()=>placeUserInSpecificSide),
    "processBinaryMatching": (()=>processBinaryMatching),
    "processDirectReferralBonus": (()=>processDirectReferralBonus),
    "searchUsersInTree": (()=>searchUsersInTree),
    "updateCachedDownlineCounts": (()=>updateCachedDownlineCounts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Calculate total downline count for a specific side
async function calculateDownlineCount(userId, side) {
    try {
        const downlineUsers = await getDownlineUsers(userId, side);
        return downlineUsers.length;
    } catch (error) {
        console.error('Downline count calculation error:', error);
        return 0;
    }
}
// Find the optimal placement position in the weaker leg
async function findOptimalPlacementPosition(referrerId) {
    try {
        // Calculate total downline counts for both sides
        const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');
        const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');
        // Determine weaker leg based on total downline count
        const weakerSide = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';
        // Find the next available spot in the weaker leg
        const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);
        if (availableSpot) {
            return availableSpot;
        }
        // Fallback: if no spot found in weaker leg, try the other side
        const strongerSide = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';
        const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);
        if (fallbackSpot) {
            return fallbackSpot;
        }
        // Final fallback: place directly under referrer
        const existingReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(referrerId);
        const hasLeft = existingReferrals.some((r)=>r.placementSide === 'LEFT');
        const hasRight = existingReferrals.some((r)=>r.placementSide === 'RIGHT');
        if (!hasLeft) {
            return {
                userId: referrerId,
                side: 'LEFT'
            };
        } else if (!hasRight) {
            return {
                userId: referrerId,
                side: 'RIGHT'
            };
        }
        // If both sides are occupied, place in the weaker side
        return {
            userId: referrerId,
            side: weakerSide
        };
    } catch (error) {
        console.error('Optimal placement position error:', error);
        // Fallback to left side
        return {
            userId: referrerId,
            side: 'LEFT'
        };
    }
}
async function placeUserInBinaryTree(referrerId, newUserId) {
    try {
        // Find optimal placement position using advanced weaker leg algorithm
        const optimalPosition = await findOptimalPlacementPosition(referrerId);
        // Create referral relationship with the optimal parent
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].create({
            referrerId: optimalPosition.userId,
            referredId: newUserId,
            placementSide: optimalPosition.side
        });
        // Update the parent's left/right referral IDs
        const updateData = optimalPosition.side === 'LEFT' ? {
            leftReferralId: newUserId
        } : {
            rightReferralId: newUserId
        };
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: optimalPosition.userId
            },
            data: updateData
        });
        // Create sponsor relationship (separate from binary placement)
        // The sponsor is always the original referrer, regardless of binary placement
        await createSponsorRelationship(referrerId, newUserId);
        // Update cached tree counts for affected users
        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);
        return optimalPosition.side;
    } catch (error) {
        console.error('Binary tree placement error:', error);
        throw error;
    }
}
// Create sponsor relationship (separate from binary placement)
async function createSponsorRelationship(sponsorId, newUserId) {
    try {
        // Update the new user's referrerId field to track sponsor
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: newUserId
            },
            data: {
                referrerId: sponsorId
            }
        });
        // Update sponsor's direct referral count
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: sponsorId
            },
            data: {
                directReferralCount: {
                    increment: 1
                },
                updatedAt: new Date()
            }
        });
        // Mark referral as direct sponsor if the binary placement parent is the same as sponsor
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.updateMany({
            where: {
                referrerId: sponsorId,
                referredId: newUserId
            },
            data: {
                isDirectSponsor: true
            }
        });
    } catch (error) {
        console.error('Sponsor relationship creation error:', error);
    // Don't throw error as this is supplementary to binary placement
    }
}
async function updateCachedDownlineCounts(userId) {
    try {
        const leftCount = await calculateDownlineCount(userId, 'LEFT');
        const rightCount = await calculateDownlineCount(userId, 'RIGHT');
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: userId
            },
            data: {
                totalLeftDownline: leftCount,
                totalRightDownline: rightCount,
                lastTreeUpdate: new Date()
            }
        });
    } catch (error) {
        console.error('Update cached downline counts error:', error);
    }
}
async function getCachedDownlineCounts(userId) {
    try {
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: userId
            },
            select: {
                totalLeftDownline: true,
                totalRightDownline: true,
                lastTreeUpdate: true
            }
        });
        if (!user) {
            return {
                left: 0,
                right: 0,
                total: 0
            };
        }
        // Check if cache is recent (within last 30 minutes for more accurate counts)
        const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;
        const cacheValidDuration = 30 * 60 * 1000; // 30 minutes
        if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {
            // Use cached values
            return {
                left: user.totalLeftDownline,
                right: user.totalRightDownline,
                total: user.totalLeftDownline + user.totalRightDownline
            };
        } else {
            // Cache is stale or missing, recalculate and update
            const leftCount = await calculateDownlineCount(userId, 'LEFT');
            const rightCount = await calculateDownlineCount(userId, 'RIGHT');
            // Update cache asynchronously
            updateCachedDownlineCounts(userId).catch(console.error);
            return {
                left: leftCount,
                right: rightCount,
                total: leftCount + rightCount
            };
        }
    } catch (error) {
        console.error('Get cached downline counts error:', error);
        return {
            left: 0,
            right: 0,
            total: 0
        };
    }
}
// Find optimal placement in specific side with weaker leg logic
async function findOptimalPlacementInSide(referrerId, targetSide) {
    try {
        // First, try to find the next available spot in the target side
        const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);
        if (availableSpot) {
            return availableSpot;
        }
        // If no spot available, find the position with smallest downline in that side
        const sideUsers = await getDownlineUsers(referrerId, targetSide);
        // Find the user with the smallest total downline in the target side
        let optimalUser = referrerId;
        let minDownlineCount = Infinity;
        for (const sideUser of sideUsers){
            const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');
            const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');
            const totalDownline = leftCount + rightCount;
            if (totalDownline < minDownlineCount) {
                // Check if this user has available spots
                const userReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(sideUser.id);
                const hasLeft = userReferrals.some((r)=>r.placementSide === 'LEFT');
                const hasRight = userReferrals.some((r)=>r.placementSide === 'RIGHT');
                if (!hasLeft || !hasRight) {
                    minDownlineCount = totalDownline;
                    optimalUser = sideUser.id;
                }
            }
        }
        // Determine which side to place in for the optimal user
        const optimalUserReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(optimalUser);
        const hasLeft = optimalUserReferrals.some((r)=>r.placementSide === 'LEFT');
        const hasRight = optimalUserReferrals.some((r)=>r.placementSide === 'RIGHT');
        if (!hasLeft) {
            return {
                userId: optimalUser,
                side: 'LEFT'
            };
        } else if (!hasRight) {
            return {
                userId: optimalUser,
                side: 'RIGHT'
            };
        }
        // If both sides occupied, use weaker leg logic
        const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');
        const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');
        const weakerSide = leftCount <= rightCount ? 'LEFT' : 'RIGHT';
        return {
            userId: optimalUser,
            side: weakerSide
        };
    } catch (error) {
        console.error('Optimal placement in side error:', error);
        return {
            userId: referrerId,
            side: targetSide
        };
    }
}
async function placeUserInSpecificSide(referrerId, newUserId, side) {
    try {
        // Find optimal placement position within the specified side
        const optimalPosition = await findOptimalPlacementInSide(referrerId, side);
        // Create referral relationship with the optimal parent
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].create({
            referrerId: optimalPosition.userId,
            referredId: newUserId,
            placementSide: optimalPosition.side
        });
        // Update the parent's referral ID
        const updateData = optimalPosition.side === 'LEFT' ? {
            leftReferralId: newUserId
        } : {
            rightReferralId: newUserId
        };
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
            where: {
                id: optimalPosition.userId
            },
            data: updateData
        });
        // Create sponsor relationship (separate from binary placement)
        await createSponsorRelationship(referrerId, newUserId);
        // Update cached tree counts for affected users
        await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);
        return optimalPosition.side;
    } catch (error) {
        console.error('Specific side placement error:', error);
        throw error;
    }
}
// Find next available spot in a specific leg
async function findNextAvailableSpotInLeg(rootUserId, targetSide) {
    try {
        // Get the first user in the target leg
        const rootReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(rootUserId);
        const firstInLeg = rootReferrals.find((r)=>r.placementSide === targetSide);
        if (!firstInLeg) {
            // The target side is completely empty
            return {
                userId: rootUserId,
                side: targetSide
            };
        }
        // Traverse down the leg to find the first available spot
        const queue = [
            firstInLeg.referredId
        ];
        while(queue.length > 0){
            const currentUserId = queue.shift();
            const currentReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(currentUserId);
            // Check if this user has any empty spots
            const hasLeft = currentReferrals.some((r)=>r.placementSide === 'LEFT');
            const hasRight = currentReferrals.some((r)=>r.placementSide === 'RIGHT');
            if (!hasLeft) {
                return {
                    userId: currentUserId,
                    side: 'LEFT'
                };
            }
            if (!hasRight) {
                return {
                    userId: currentUserId,
                    side: 'RIGHT'
                };
            }
            // Add children to queue for further traversal
            currentReferrals.forEach((r)=>{
                queue.push(r.referredId);
            });
        }
        return null; // No available spot found
    } catch (error) {
        console.error('Find available spot error:', error);
        return null;
    }
}
async function processDirectReferralBonus(referrerId, investmentAmount) {
    try {
        const bonusPercentage = parseFloat(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('DIRECT_REFERRAL_BONUS') || '10');
        const bonusAmount = investmentAmount * bonusPercentage / 100;
        // Add commission directly to sponsor's wallet balance
        const sponsorWallet = await walletBalanceDb.getOrCreate(referrerId);
        await walletBalanceDb.updateBalance(referrerId, {
            availableBalance: sponsorWallet.availableBalance + bonusAmount
        });
        // Create direct referral transaction
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transactionDb"].create({
            userId: referrerId,
            type: 'DIRECT_REFERRAL',
            amount: bonusAmount,
            description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount})`,
            status: 'COMPLETED'
        });
        // Update referral commission earned
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.updateMany({
            where: {
                referrerId,
                referred: {
                    miningUnits: {
                        some: {
                            investmentAmount
                        }
                    }
                }
            },
            data: {
                commissionEarned: {
                    increment: bonusAmount
                }
            }
        });
        return bonusAmount;
    } catch (error) {
        console.error('Direct referral bonus error:', error);
        throw error;
    }
}
async function addBinaryPoints(userId, investmentAmount) {
    try {
        // Calculate points: $100 investment = 1 point
        const points = Math.floor(investmentAmount / 100);
        if (points <= 0) return; // No points to add if investment is less than $100
        // Find all upline users and add points to their binary system (only active users)
        const uplineUsers = await getActiveUplineUsers(userId);
        for (const uplineUser of uplineUsers){
            // Determine which side this user is on relative to upline
            const placementSide = await getUserPlacementSide(uplineUser.id, userId);
            if (placementSide) {
                // Add points to the appropriate side
                const pointsToAdd = placementSide === 'LEFT' ? {
                    leftPoints: points
                } : {
                    rightPoints: points
                };
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["binaryPointsDb"].upsert({
                    userId: uplineUser.id,
                    ...pointsToAdd
                });
                console.log(`Added ${points} points to ${placementSide} side for user ${uplineUser.id}`);
            }
        }
    } catch (error) {
        console.error('Binary points addition error:', error);
        throw error;
    }
}
// Get all upline users for a given user
async function getUplineUsers(userId) {
    try {
        const uplineUsers = [];
        let currentUserId = userId;
        // Traverse up the tree (maximum 10 levels to prevent infinite loops)
        for(let level = 0; level < 10; level++){
            const referral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referredId: currentUserId
                },
                include: {
                    referrer: {
                        select: {
                            id: true,
                            email: true
                        }
                    }
                }
            });
            if (!referral) break;
            uplineUsers.push(referral.referrer);
            currentUserId = referral.referrerId;
        }
        return uplineUsers;
    } catch (error) {
        console.error('Upline users fetch error:', error);
        return [];
    }
}
// Get all ACTIVE upline users for a given user (skip inactive users)
async function getActiveUplineUsers(userId) {
    try {
        const uplineUsers = [];
        let currentUserId = userId;
        // Traverse up the tree (maximum 10 levels to prevent infinite loops)
        for(let level = 0; level < 10; level++){
            const referral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referredId: currentUserId
                },
                include: {
                    referrer: {
                        select: {
                            id: true,
                            email: true,
                            isActive: true
                        }
                    }
                }
            });
            if (!referral) break;
            // Only add active users to the list
            if (referral.referrer.isActive) {
                uplineUsers.push(referral.referrer);
            }
            // Continue traversing up regardless of active status
            currentUserId = referral.referrerId;
        }
        return uplineUsers;
    } catch (error) {
        console.error('Active upline users fetch error:', error);
        return [];
    }
}
// Determine which side a user is on relative to an upline user
async function getUserPlacementSide(uplineUserId, userId) {
    try {
        // Check direct placement first
        const directReferral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
            where: {
                referrerId: uplineUserId,
                referredId: userId
            }
        });
        if (directReferral) {
            return directReferral.placementSide;
        }
        // Check indirect placement by traversing down the tree
        const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');
        const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');
        if (leftSideUsers.some((u)=>u.id === userId)) {
            return 'LEFT';
        }
        if (rightSideUsers.some((u)=>u.id === userId)) {
            return 'RIGHT';
        }
        return null;
    } catch (error) {
        console.error('Placement side determination error:', error);
        return null;
    }
}
// Get all downline users for a specific side
async function getDownlineUsers(userId, side) {
    try {
        const downlineUsers = [];
        const visited = new Set();
        // Start with the direct placement on the specified side
        const initialReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
            where: {
                referrerId: userId,
                placementSide: side
            },
            select: {
                referredId: true
            }
        });
        // Use BFS to traverse the entire subtree
        const queue = initialReferrals.map((r)=>r.referredId);
        while(queue.length > 0){
            const currentUserId = queue.shift();
            // Skip if already visited (prevent infinite loops)
            if (visited.has(currentUserId)) continue;
            visited.add(currentUserId);
            // Add current user to downline
            downlineUsers.push({
                id: currentUserId
            });
            // Get all referrals (both LEFT and RIGHT) from current user
            const referrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
                where: {
                    referrerId: currentUserId
                },
                select: {
                    referredId: true
                }
            });
            // Add all children to queue for further traversal
            for (const referral of referrals){
                if (!visited.has(referral.referredId)) {
                    queue.push(referral.referredId);
                }
            }
        }
        return downlineUsers;
    } catch (error) {
        console.error('Downline users fetch error:', error);
        return [];
    }
}
// Get all downline users (both sides combined) for total team count
async function getAllDownlineUsers(userId) {
    try {
        const downlineUsers = [];
        const visited = new Set();
        // Get all direct referrals (both LEFT and RIGHT)
        const initialReferrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
            where: {
                referrerId: userId
            },
            select: {
                referredId: true
            }
        });
        // Use BFS to traverse the entire binary tree
        const queue = initialReferrals.map((r)=>r.referredId);
        while(queue.length > 0){
            const currentUserId = queue.shift();
            // Skip if already visited (prevent infinite loops)
            if (visited.has(currentUserId)) continue;
            visited.add(currentUserId);
            // Get user info including active status
            const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                where: {
                    id: currentUserId
                },
                select: {
                    id: true,
                    isActive: true
                }
            });
            if (user) {
                downlineUsers.push({
                    id: user.id,
                    isActive: user.isActive
                });
                // Get all referrals from current user
                const referrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
                    where: {
                        referrerId: currentUserId
                    },
                    select: {
                        referredId: true
                    }
                });
                // Add all children to queue for further traversal
                for (const referral of referrals){
                    if (!visited.has(referral.referredId)) {
                        queue.push(referral.referredId);
                    }
                }
            }
        }
        return downlineUsers;
    } catch (error) {
        console.error('All downline users fetch error:', error);
        return [];
    }
}
async function processBinaryMatching() {
    try {
        console.log('Starting binary matching process...');
        const maxPointsPerSide = parseFloat(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('MAX_BINARY_POINTS_PER_SIDE') || '2000');
        const binaryPoolPercentage = parseFloat(await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["adminSettingsDb"].get('BINARY_POOL_PERCENTAGE') || '30');
        // Get current binary pool amount (weekly accumulation)
        const totalInvestments = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].miningUnit.aggregate({
            _sum: {
                investmentAmount: true
            },
            where: {
                createdAt: {
                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                }
            }
        });
        const weeklyBinaryPool = (totalInvestments._sum.investmentAmount || 0) * binaryPoolPercentage / 100;
        // Get all users with binary points
        const usersWithPoints = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.findMany({
            where: {
                OR: [
                    {
                        leftPoints: {
                            gt: 0
                        }
                    },
                    {
                        rightPoints: {
                            gt: 0
                        }
                    }
                ]
            },
            include: {
                user: {
                    select: {
                        id: true,
                        email: true
                    }
                }
            }
        });
        console.log(`Processing binary matching for ${usersWithPoints.length} users`);
        let totalMatchedPoints = 0;
        const matchingResults = [];
        for (const userPoints of usersWithPoints){
            try {
                // Calculate matching points (minimum of left and right, capped at max per side)
                const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);
                const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);
                const matchedPoints = Math.min(leftPoints, rightPoints);
                if (matchedPoints > 0) {
                    // Calculate payout from binary pool
                    const userPayout = matchedPoints / Math.max(totalMatchedPoints, 1) * weeklyBinaryPool;
                    if (userPayout > 0) {
                        // Create binary bonus transaction
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transactionDb"].create({
                            userId: userPoints.userId,
                            type: 'BINARY_BONUS',
                            amount: userPayout,
                            description: `Weekly binary matching bonus - ${matchedPoints} points matched`,
                            status: 'COMPLETED'
                        });
                    }
                    // Implement pressure-out logic: flush excess points beyond max cap
                    const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);
                    const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);
                    // Apply pressure-out: if remaining points exceed max cap, flush them
                    const finalLeftPoints = Math.min(remainingLeftPoints, maxPointsPerSide);
                    const finalRightPoints = Math.min(remainingRightPoints, maxPointsPerSide);
                    const flushedLeftPoints = remainingLeftPoints - finalLeftPoints;
                    const flushedRightPoints = remainingRightPoints - finalRightPoints;
                    // Update matched points and apply pressure-out flush
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].binaryPoints.update({
                        where: {
                            id: userPoints.id
                        },
                        data: {
                            leftPoints: finalLeftPoints,
                            rightPoints: finalRightPoints,
                            matchedPoints: {
                                increment: matchedPoints
                            },
                            flushDate: new Date()
                        }
                    });
                    // Log pressure-out activity if points were flushed
                    if (flushedLeftPoints > 0 || flushedRightPoints > 0) {
                        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
                            action: 'BINARY_PRESSURE_OUT',
                            userId: userPoints.userId,
                            details: {
                                flushedLeftPoints,
                                flushedRightPoints,
                                totalFlushed: flushedLeftPoints + flushedRightPoints,
                                reason: 'Excess points beyond max cap',
                                timestamp: new Date().toISOString()
                            }
                        });
                    }
                    matchingResults.push({
                        userId: userPoints.userId,
                        matchedPoints,
                        payout: userPayout
                    });
                    totalMatchedPoints += matchedPoints;
                }
            } catch (userError) {
                console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);
            }
        }
        // Log the binary matching process
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["systemLogDb"].create({
            action: 'BINARY_MATCHING_PROCESSED',
            details: {
                usersProcessed: usersWithPoints.length,
                totalMatchedPoints,
                weeklyBinaryPool,
                totalPayouts: matchingResults.reduce((sum, r)=>sum + r.payout, 0),
                timestamp: new Date().toISOString()
            }
        });
        console.log(`Binary matching completed. Processed ${matchingResults.length} users with ${totalMatchedPoints} total matched points.`);
        return matchingResults;
    } catch (error) {
        console.error('Binary matching process error:', error);
        throw error;
    }
}
async function getSponsorInfo(userId) {
    try {
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: userId
            },
            select: {
                referrerId: true
            }
        });
        if (!user?.referrerId) return null;
        const sponsor = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: user.referrerId
            },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true
            }
        });
        return sponsor;
    } catch (error) {
        console.error('Sponsor info fetch error:', error);
        return null;
    }
}
async function getDirectReferralCount(userId) {
    try {
        const count = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count({
            where: {
                referrerId: userId
            }
        });
        return count;
    } catch (error) {
        console.error('Direct referral count error:', error);
        return 0;
    }
}
async function getTotalTeamCount(userId) {
    try {
        return await getCachedDownlineCounts(userId);
    } catch (error) {
        console.error('Total team count error:', error);
        return {
            left: 0,
            right: 0,
            total: 0
        };
    }
}
async function getDetailedTeamStats(userId) {
    try {
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: userId
            },
            select: {
                directReferralCount: true
            }
        });
        const teamCounts = await getCachedDownlineCounts(userId);
        // Get all downline users for accurate active member count
        const allDownlineUsers = await getAllDownlineUsers(userId);
        const activeMembers = allDownlineUsers.filter((u)=>u.isActive).length;
        // Get recent joins (last 30 days) - direct referrals only
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const recentJoins = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.count({
            where: {
                referrerId: userId,
                createdAt: {
                    gte: thirtyDaysAgo
                }
            }
        });
        return {
            directReferrals: user?.directReferralCount || 0,
            leftTeam: teamCounts.left,
            rightTeam: teamCounts.right,
            totalTeam: teamCounts.total,
            activeMembers,
            recentJoins
        };
    } catch (error) {
        console.error('Detailed team stats error:', error);
        return {
            directReferrals: 0,
            leftTeam: 0,
            rightTeam: 0,
            totalTeam: 0,
            activeMembers: 0,
            recentJoins: 0
        };
    }
}
async function getUsersByGeneration(userId, generation) {
    try {
        if (generation <= 0) return [];
        let currentLevelUsers = [
            {
                id: userId,
                side: null
            }
        ];
        for(let level = 1; level <= generation; level++){
            const nextLevelUsers = [];
            for (const currentUser of currentLevelUsers){
                const referrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
                    where: {
                        referrerId: currentUser.id
                    },
                    include: {
                        referred: {
                            select: {
                                id: true,
                                email: true,
                                firstName: true,
                                lastName: true,
                                createdAt: true
                            }
                        }
                    }
                });
                for (const referral of referrals){
                    nextLevelUsers.push({
                        id: referral.referredId,
                        side: referral.placementSide
                    });
                }
            }
            currentLevelUsers = nextLevelUsers;
        }
        // Get full user details for the final generation
        const userDetails = await Promise.all(currentLevelUsers.map(async (user)=>{
            const userInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                where: {
                    id: user.id
                },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    createdAt: true
                }
            });
            return {
                ...userInfo,
                placementSide: user.side
            };
        }));
        return userDetails.filter(Boolean);
    } catch (error) {
        console.error('Users by generation error:', error);
        return [];
    }
}
async function getBinaryTreeStructure(userId, depth = 3, expandedNodes = new Set()) {
    try {
        const buildTree = async (currentUserId, currentDepth, path = '')=>{
            if (currentDepth <= 0) return null;
            const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                where: {
                    id: currentUserId
                },
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    createdAt: true,
                    isActive: true
                }
            });
            if (!user) return null;
            // Get sponsor information
            const sponsorInfo = await getSponsorInfo(currentUserId);
            // Get direct referral count
            const directReferralCount = await getDirectReferralCount(currentUserId);
            // Get team counts
            const teamCounts = await getTotalTeamCount(currentUserId);
            // Get direct referrals (binary placement)
            const leftReferral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referrerId: currentUserId,
                    placementSide: 'LEFT'
                },
                include: {
                    referred: {
                        select: {
                            id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                            createdAt: true,
                            isActive: true
                        }
                    }
                }
            });
            const rightReferral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referrerId: currentUserId,
                    placementSide: 'RIGHT'
                },
                include: {
                    referred: {
                        select: {
                            id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                            createdAt: true,
                            isActive: true
                        }
                    }
                }
            });
            // Get binary points
            const binaryPoints = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["binaryPointsDb"].findByUserId(currentUserId);
            // Determine if we should load children
            // Load children if:
            // 1. We're within the initial depth (first 3 levels)
            // 2. OR this node is explicitly expanded
            const shouldLoadChildren = currentDepth > 1 && (path.length < 3 || // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)
            expandedNodes.has(currentUserId));
            // Check if children exist (for showing expand button)
            const hasLeftChild = leftReferral !== null;
            const hasRightChild = rightReferral !== null;
            return {
                user,
                sponsorInfo,
                directReferralCount,
                teamCounts,
                binaryPoints: binaryPoints || {
                    leftPoints: 0,
                    rightPoints: 0,
                    matchedPoints: 0
                },
                hasLeftChild,
                hasRightChild,
                leftChild: shouldLoadChildren && leftReferral ? await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,
                rightChild: shouldLoadChildren && rightReferral ? await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null
            };
        };
        return await buildTree(userId, depth);
    } catch (error) {
        console.error('Binary tree structure error:', error);
        throw error;
    }
}
async function loadNodeChildren(userId) {
    try {
        // Get direct referrals (binary placement)
        const leftReferral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
            where: {
                referrerId: userId,
                placementSide: 'LEFT'
            },
            include: {
                referred: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                        createdAt: true,
                        isActive: true
                    }
                }
            }
        });
        const rightReferral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
            where: {
                referrerId: userId,
                placementSide: 'RIGHT'
            },
            include: {
                referred: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                        createdAt: true,
                        isActive: true
                    }
                }
            }
        });
        const buildChildNode = async (referral)=>{
            if (!referral) return null;
            const childUserId = referral.referredId;
            // Get sponsor information
            const sponsorInfo = await getSponsorInfo(childUserId);
            // Get direct referral count
            const directReferralCount = await getDirectReferralCount(childUserId);
            // Get team counts
            const teamCounts = await getTotalTeamCount(childUserId);
            // Get binary points
            const binaryPoints = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["binaryPointsDb"].findByUserId(childUserId);
            // Check if this child has its own children
            const hasLeftChild = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referrerId: childUserId,
                    placementSide: 'LEFT'
                },
                select: {
                    id: true
                }
            }) !== null;
            const hasRightChild = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referrerId: childUserId,
                    placementSide: 'RIGHT'
                },
                select: {
                    id: true
                }
            }) !== null;
            return {
                user: referral.referred,
                sponsorInfo,
                directReferralCount,
                teamCounts,
                binaryPoints: binaryPoints || {
                    leftPoints: 0,
                    rightPoints: 0,
                    matchedPoints: 0
                },
                hasLeftChild,
                hasRightChild,
                leftChild: null,
                rightChild: null
            };
        };
        const leftChild = await buildChildNode(leftReferral);
        const rightChild = await buildChildNode(rightReferral);
        return {
            leftChild,
            rightChild
        };
    } catch (error) {
        console.error('Load node children error:', error);
        return {
            leftChild: null,
            rightChild: null
        };
    }
}
async function searchUsersInTree(rootUserId, searchTerm, maxResults = 20) {
    try {
        const searchPattern = `%${searchTerm.toLowerCase()}%`;
        // Get all downline users that match the search term
        const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');
        const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');
        const allDownlineIds = [
            ...leftUsers,
            ...rightUsers
        ].map((u)=>u.id);
        if (allDownlineIds.length === 0) return [];
        const matchingUsers = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
            where: {
                id: {
                    in: allDownlineIds
                },
                OR: [
                    {
                        email: {
                            contains: searchTerm,
                            mode: 'insensitive'
                        }
                    },
                    {
                        firstName: {
                            contains: searchTerm,
                            mode: 'insensitive'
                        }
                    },
                    {
                        lastName: {
                            contains: searchTerm,
                            mode: 'insensitive'
                        }
                    }
                ]
            },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                createdAt: true,
                referrerId: true
            },
            take: maxResults
        });
        // Get placement path and sponsor info for each user
        const results = await Promise.all(matchingUsers.map(async (user)=>{
            const placementPath = await getPlacementPath(rootUserId, user.id);
            const generation = placementPath.split('-').length;
            let sponsorInfo = undefined;
            if (user.referrerId) {
                sponsorInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        id: user.referrerId
                    },
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true
                    }
                });
            }
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                createdAt: user.createdAt,
                placementPath,
                generation,
                sponsorInfo: sponsorInfo || undefined
            };
        }));
        return results;
    } catch (error) {
        console.error('Search users in tree error:', error);
        return [];
    }
}
// Get placement path from root to a specific user (e.g., "L-R-L")
async function getPlacementPath(rootUserId, targetUserId) {
    try {
        if (rootUserId === targetUserId) return 'ROOT';
        const path = [];
        let currentUserId = targetUserId;
        // Traverse up the tree to find path
        while(currentUserId !== rootUserId){
            const referral = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findFirst({
                where: {
                    referredId: currentUserId
                }
            });
            if (!referral) break;
            path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');
            currentUserId = referral.referrerId;
            // Prevent infinite loops
            if (path.length > 20) break;
        }
        return path.join('-') || 'UNKNOWN';
    } catch (error) {
        console.error('Get placement path error:', error);
        return 'UNKNOWN';
    }
}
// Update tree counts after a new user placement
async function updateTreeCountsAfterPlacement(sponsorId, placementParentId) {
    try {
        // Update counts for the sponsor (if different from placement parent)
        if (sponsorId !== placementParentId) {
            await updateCachedDownlineCounts(sponsorId);
        }
        // Update counts for the placement parent
        await updateCachedDownlineCounts(placementParentId);
        // Update counts for all upline users from the placement parent
        const uplineUsers = await getUplineUsers(placementParentId);
        const updatePromises = uplineUsers.map((user)=>updateCachedDownlineCounts(user.id));
        await Promise.all(updatePromises);
    } catch (error) {
        console.error('Update tree counts after placement error:', error);
    // Don't throw error as this is supplementary to placement
    }
}
async function bulkUpdateTreeCounts(userIds) {
    try {
        const updatePromises = userIds.map((userId)=>updateCachedDownlineCounts(userId));
        await Promise.all(updatePromises);
    } catch (error) {
        console.error('Bulk update tree counts error:', error);
    }
}
async function getTreeHealthStats(rootUserId) {
    try {
        const teamCounts = await getCachedDownlineCounts(rootUserId);
        const totalUsers = teamCounts.total;
        // Calculate balance ratio
        const smallerSide = Math.min(teamCounts.left, teamCounts.right);
        const largerSide = Math.max(teamCounts.left, teamCounts.right);
        const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;
        // Calculate tree depth statistics
        let maxDepth = 0;
        let totalDepth = 0;
        let userCount = 0;
        // BFS to calculate depths
        const queue = [
            {
                userId: rootUserId,
                depth: 0
            }
        ];
        const visited = new Set();
        while(queue.length > 0){
            const { userId, depth } = queue.shift();
            if (visited.has(userId)) continue;
            visited.add(userId);
            maxDepth = Math.max(maxDepth, depth);
            totalDepth += depth;
            userCount++;
            const referrals = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].referral.findMany({
                where: {
                    referrerId: userId
                },
                select: {
                    referredId: true
                }
            });
            for (const referral of referrals){
                if (!visited.has(referral.referredId)) {
                    queue.push({
                        userId: referral.referredId,
                        depth: depth + 1
                    });
                }
            }
        }
        const averageDepth = userCount > 0 ? totalDepth / userCount : 0;
        // Calculate empty positions (theoretical max - actual users)
        const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;
        const emptyPositions = Math.max(0, theoreticalMax - totalUsers);
        return {
            totalUsers,
            balanceRatio,
            averageDepth,
            maxDepth,
            emptyPositions
        };
    } catch (error) {
        console.error('Tree health stats error:', error);
        return {
            totalUsers: 0,
            balanceRatio: 1,
            averageDepth: 0,
            maxDepth: 0,
            emptyPositions: 0
        };
    }
}
}}),
"[project]/src/app/api/referrals/tree/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/referral.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
;
;
async function GET(request) {
    try {
        const { authenticated, user } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authenticateRequest"])(request);
        if (!authenticated || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Not authenticated'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const depth = parseInt(searchParams.get('depth') || '3');
        const enhanced = searchParams.get('enhanced') === 'true';
        const expandedNodes = searchParams.get('expanded') ? new Set(searchParams.get('expanded').split(',').filter((id)=>id.length > 0)) : new Set();
        const nodeId = searchParams.get('nodeId'); // For loading specific node children
        // If requesting specific node children
        if (nodeId) {
            const children = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loadNodeChildren"])(nodeId);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: children
            });
        }
        // Get binary tree structure with expanded nodes
        const treeStructure = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBinaryTreeStructure"])(user.id, Math.min(depth, 15), expandedNodes); // Increased max depth
        // Get user's referral statistics - use user table for direct referrals (sponsor relationships)
        const directReferralsFromUserTable = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
            where: {
                referrerId: user.id
            },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                isActive: true,
                createdAt: true
            }
        });
        // Get binary tree placement data from referral table
        const binaryPlacements = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["referralDb"].findByReferrerId(user.id);
        const binaryPoints = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["binaryPointsDb"].findByUserId(user.id);
        // Calculate placement statistics
        const leftPlacements = binaryPlacements.filter((r)=>r.placementSide === 'LEFT').length;
        const rightPlacements = binaryPlacements.filter((r)=>r.placementSide === 'RIGHT').length;
        // Get total team counts (including all downliners)
        const teamCounts = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getTotalTeamCount"])(user.id);
        // Calculate total commissions from transactions
        const commissionTransactions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["transactionDb"].findByUserId(user.id, {
            types: [
                'DIRECT_REFERRAL',
                'BINARY_BONUS'
            ],
            status: 'COMPLETED'
        });
        const totalCommissions = commissionTransactions.reduce((sum, t)=>sum + t.amount, 0);
        let responseData = {
            treeStructure,
            statistics: {
                totalDirectReferrals: directReferralsFromUserTable.length,
                leftPlacements,
                rightPlacements,
                leftReferrals: teamCounts.left,
                rightReferrals: teamCounts.right,
                leftTeam: teamCounts.left,
                rightTeam: teamCounts.right,
                totalTeam: teamCounts.total,
                totalCommissions,
                binaryPoints: binaryPoints || {
                    leftPoints: 0,
                    rightPoints: 0,
                    matchedPoints: 0
                }
            },
            referralLinks: {
                left: `${("TURBOPACK compile-time value", "http://localhost:3000")}/register?ref=${user.referralId}&side=left`,
                right: `${("TURBOPACK compile-time value", "http://localhost:3000")}/register?ref=${user.referralId}&side=right`,
                general: `${("TURBOPACK compile-time value", "http://localhost:3000")}/register?ref=${user.referralId}`
            }
        };
        // Add enhanced statistics if requested
        if (enhanced) {
            const detailedStats = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDetailedTeamStats"])(user.id);
            const treeHealth = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$referral$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getTreeHealthStats"])(user.id);
            responseData.statistics.detailedStats = detailedStats;
            responseData.statistics.treeHealth = treeHealth;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: responseData
        });
    } catch (error) {
        console.error('Binary tree fetch error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch binary tree'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f0326f48._.js.map